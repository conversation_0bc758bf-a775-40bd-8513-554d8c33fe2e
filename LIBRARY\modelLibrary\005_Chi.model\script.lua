-- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  G = ADekoLib
  a = 50
  c = 10
  blockNoX = 2
  blockNoY = 3
  cW = 20
  cT = 10
  windowDepth = 18   --on yuz kanal
  local windowDepthBack = materialThickness - windowDepth
   
  extEdgeVtoolExist       	= 0   -- D<PERSON><PERSON> kenar <PERSON>h işlemi var mı? derinlik/0:yok
  edgeCornerRExist          = 3   -- <PERSON><PERSON>k köşe Radüsü var mı? derinlik/0:yok

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  local xLimit1 = 400 -- yana 2. pencere koymak icin alt limit "Dahil"
  local xLimit2 = 800 -- yana 3. pencere koymak icin alt limit "Dahil"
  local xLimit3 = 1200 -- yana 4. pencere koymak icin alt limit "Dahil"
  
  local yLimit1 = 400 -- uste 2. pencere koymak icin alt limit "Dahil"
  local yLimit2 = 800 -- uste 3. pencere koymak icin alt limit "Dahil"
  local yLimit3 = 1200 -- uste 4. pencere koymak icin alt limit "Dahil"
  
  if blockNoX == 0 then
    blockNoX = 1
    if X >= xLimit1 and X < xLimit2 then
      blockNoX = 2
    elseif X >= xLimit2 and X < xLimit3 then
      blockNoX = 3
    elseif X >= xLimit3 then
      blockNoX = 4
    end
  end
  
  if blockNoY == 0 then
     blockNoY = 1
    if Y >= yLimit1 and Y < yLimit2 then
      blockNoY = 2
    elseif Y >= yLimit2 and X < yLimit3 then
      blockNoY = 3
    elseif Y >= yLimit3 then
      blockNoY = 4
    end
  end
  
  local bulge = math.tan(math.pi/8)

  G.setThickness(-materialThickness)
  G.setFace("top")
  if edgeCornerRExist >0 then           ---köşe radüsü yapsın mı
    G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
      {X-edgeCornerRExist,0,0,bulge},
      {X,edgeCornerRExist},
      {X,Y-edgeCornerRExist,0,bulge},
      {X-edgeCornerRExist,Y},
      {edgeCornerRExist,Y,0,bulge},
      {0,Y-edgeCornerRExist},
      {0,edgeCornerRExist,0,bulge},
      {edgeCornerRExist,0})
  else
    G.makePartShape()
  end
  
  if extEdgeVtoolExist > 0 then
	-- G.setLayer("K_AciliV" ..aV)	
	G.setLayer("K_AciliV_Pah")	
    G.setThickness(-extEdgeVtoolExist)
    G.rectangle({0,0},{X,Y})
  end
    
  local stepX = (blockNoX-1)		--Number of laths on X axis
  local stepY = (blockNoY-1)		--Number of laths on Y axis
  local innerX = X-2*a-stepX*c		--Width of the inner rectangle on the x axis
  local innerY = Y-2*a-stepY*c		--Length of the inner rectangle on the y axis
  local correctedblockX = innerX / blockNoX
  local correctedblockY = innerY / blockNoY
  
  if X<140 or Y<140 then
    print("Part dimension too small")
    return true
  end
  
  for i=0,stepY do
    G.setLayer("H_Freze"..cT.."mm_Ic")		--Seperation of the blocks                            
    G.setThickness(-windowDepth)
    local y = a+(i)*c+(i+0.5)*(correctedblockY)
    for j=0,stepX do
      G.setLayer("H_Freze"..cT.."mm_Ic")		--Seperation of the blocks 
      G.setThickness(-windowDepth)
      local x = a+(j)*c+(j+0.5)*(correctedblockX)
      pc1 = {x-(c/(math.sqrt(2))),y}
      pc2 = {x,y+(c/(math.sqrt(2)))}
      pc3 = {x+(c/(math.sqrt(2))),y}
      pc4 = {x,y-(c/(math.sqrt(2)))}
      p1x = {x-(correctedblockX)/2+(c/(math.sqrt(2))),y-(correctedblockY)/2}
      p1y = {x-(correctedblockX)/2,y-(correctedblockY)/2+(c/(math.sqrt(2)))}
      p2x = {x-(correctedblockX)/2+(c/(math.sqrt(2))),y+(correctedblockY)/2}
      p2y = {x-(correctedblockX)/2,y+(correctedblockY)/2-(c/(math.sqrt(2)))}
      p3x = {x+(correctedblockX)/2-(c/(math.sqrt(2))),y+(correctedblockY)/2}
      p3y = {x+(correctedblockX)/2,y+(correctedblockY)/2-(c/(math.sqrt(2)))}
      p4x = {x+(correctedblockX)/2-(c/(math.sqrt(2))),y-(correctedblockY)/2}
      p4y = {x+(correctedblockX)/2,y-(correctedblockY)/2+(c/(math.sqrt(2)))}
      G.polyline(pc1,p1y,p2y,pc1)
      G.polyline(pc2,p2x,p3x,pc2)
      G.polyline(pc3,p3y,p4y,pc3)
      G.polyline(pc4,p4x,p1x,pc4)
    end
  end
  
  G.setFace("bottom")
  
  G.setLayer("K_Freze_" .. cW .. "mm_SF")		--Clearing procedures of the backside
  for i=0,stepY do
    G.setThickness(-windowDepthBack)
    local y = a+(i)*c+(i+0.5)*(correctedblockY)
    for j=0,stepX do
      G.setThickness(-windowDepthBack)
      local x = a+(j)*c+(j+0.5)*(correctedblockX)
      point1 = {x-(correctedblockX)/2,y-(correctedblockY)/2}
      point2 = {x-(correctedblockX)/2,y+(correctedblockY)/2}
      point3 = {x+(correctedblockX)/2,y+(correctedblockY)/2}
      point4 = {x+(correctedblockX)/2,y-(correctedblockY)/2}
      G.line(point1,point3)
      G.line(point2,point4)
    end
  end
  
  for i = 0, stepX-1 do
    G.setThickness(-windowDepthBack)
    local y = a
    local x = a+(i+1)*correctedblockX+i*c+c/2
    point1 = {x,y}
    point2 = {x,Y-y}
    G.line(point1,point2,0)
  end
  
  for j = 0, stepY-1 do
    G.setThickness(-windowDepthBack)
    local x = a
    local y = a+(j+1)*correctedblockY+j*c+c/2
    point1 = {x,y}
    point2 = {X-x,y}
    G.line(point1,point2,0)
  end
  
  G.setLayer("H_Freze"..cW.."mm_Ic_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a-10, a-10}, {X-a+10, Y-a+10})
  
  G.setLayer("K_Freze_" .. cW .. "mm_SF")
  G.setThickness(-windowDepthBack)
  G.rectangle({a, a}, {X-a, Y-a})
  
  return true
end

require "ADekoDebugMode"