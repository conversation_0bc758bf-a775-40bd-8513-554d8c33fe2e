import type {
  C<PERSON>Tool,
  Cylind<PERSON>Tool,
  DoorMachiningProfile,

  DrawCommand
} from '@/types'

// CNC Tool Service - Basic Tool Management (OCJS functionality removed)
export class CNCToolService {
  private static instance: CNCToolService
  private tools: Map<string, CNCTool> = new Map()
  private machiningProfiles: Map<string, DoorMachiningProfile> = new Map()

  private constructor() {
    this.initializeDefaultTools()
    this.initializeDefaultProfiles()
  }

  public static getInstance(): CNCToolService {
    if (!CNCToolService.instance) {
      CNCToolService.instance = new CNCToolService()
    }
    return CNCToolService.instance
  }

  private initializeDefaultTools(): void {
    // Cylindrical tools for general machining
    const cylindrical6mm: CylindricalTool = {
      id: 'cyl-6mm',
      name: '6mm End Mill',
      shape: 'cylindrical',
      units: 'metric',
      diameter: 6,
      length: 50,
      flutes: 2,
      helixAngle: 30,
      description: 'Standard end mill for general machining',
      material: 'Carbide',
      coating: 'TiN'
    }

    // Add basic tools
    this.tools.set(cylindrical6mm.id, cylindrical6mm)
  }

  private initializeDefaultProfiles(): void {
    const standardProfile: DoorMachiningProfile = {
      id: 'standard-door',
      name: 'Standard Door Profile',
      description: 'Standard machining profile for cabinet doors',
      topSurfaceOperations: [],
      bottomSurfaceOperations: [],
      tools: []
    }

    this.machiningProfiles.set(standardProfile.id, standardProfile)
  }

  // Basic tool management methods
  public getAllTools(): CNCTool[] {
    return Array.from(this.tools.values())
  }

  public getToolById(id: string): CNCTool | null {
    return this.tools.get(id) || null
  }

  public addTool(tool: CNCTool): void {
    this.tools.set(tool.id, tool)
  }

  public removeTool(id: string): boolean {
    return this.tools.delete(id)
  }

  public updateTool(tool: CNCTool): void {
    this.tools.set(tool.id, tool)
  }

  // Machining profile methods
  public getAllProfiles(): DoorMachiningProfile[] {
    return Array.from(this.machiningProfiles.values())
  }

  public getProfileById(id: string): DoorMachiningProfile | null {
    return this.machiningProfiles.get(id) || null
  }

  public addProfile(profile: DoorMachiningProfile): void {
    this.machiningProfiles.set(profile.id, profile)
  }

  public removeProfile(id: string): boolean {
    return this.machiningProfiles.delete(id)
  }

  // Get tools by shape for layer tool detector
  public getToolsByShape(shape: string): CNCTool[] {
    return Array.from(this.tools.values()).filter(tool => tool.shape === shape)
  }

  // Tool detection methods
  public detectToolFromLayerName(_layerName: string): CNCTool | null {
    // Default tool
    return this.getToolById('cyl-6mm')
  }

  public detectOperationFromLayer(_layerName: string): string {
    return 'profile' // Default operation
  }

  public determineFaceFromLayer(_layerName: string): 'top' | 'bottom' {
    return 'top' // Default to top face
  }

  // Layer exclusion methods
  public shouldExcludeLayerFromMachining(layerName: string): boolean {
    const upperLayer = layerName.toUpperCase()
    
    // Exclude annotation and measurement layers
    if (upperLayer === 'LABELS' || upperLayer === 'PARAMETERS' || upperLayer === 'POINTS') {
      return true
    }
    
    // Exclude LMM layers (measurement/markup)
    if (upperLayer.startsWith('LMM')) {
      return true
    }
    
    return false
  }

  // Parse layer data for basic operations (OCJS functionality removed)
  public parseLayerForOCJS(layerName: string, _commands: DrawCommand[]): {
    tool: CNCTool | null
    operation: string
    depth: number
    face: 'top' | 'bottom'
  } {
    const tool = this.detectToolFromLayerName(layerName)
    const operation = this.detectOperationFromLayer(layerName)
    const face = this.determineFaceFromLayer(layerName)
    const depth = 5 // Default depth

    return {
      tool,
      operation,
      depth,
      face
    }
  }

  // Group commands by layer and parse (OCJS functionality removed)
  public parseCommandsForOCJS(commands: DrawCommand[]): {
    panelCommands: DrawCommand[]
    topTools: { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[]
    bottomTools: { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[]
  } {
    const layerGroups = new Map<string, DrawCommand[]>()
    
    // Group commands by layer
    commands.forEach(command => {
      const layerName = command.layer_name || 'DEFAULT'
      if (!layerGroups.has(layerName)) {
        layerGroups.set(layerName, [])
      }
      layerGroups.get(layerName)!.push(command)
    })

    const result = {
      panelCommands: [] as DrawCommand[],
      topTools: [] as { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[],
      bottomTools: [] as { tool: CNCTool; commands: DrawCommand[]; depth: number; operation: string }[]
    }

    // Process each layer
    layerGroups.forEach((layerCommands, layerName) => {
      if (layerName === 'PANEL') {
        // PANEL layer defines the door body
        result.panelCommands = layerCommands
      } else if (!this.shouldExcludeLayerFromMachining(layerName)) {
        // Parse tool layer
        const parsed = this.parseLayerForOCJS(layerName, layerCommands)

        if (parsed.tool) {
          const toolData = {
            tool: parsed.tool,
            commands: layerCommands,
            depth: parsed.depth,
            operation: parsed.operation
          }

          if (parsed.face === 'bottom') {
            result.bottomTools.push(toolData)
          } else {
            result.topTools.push(toolData)
          }
        }
      }
    })

    return result
  }
}

// Export singleton instance
export const cncToolService = CNCToolService.getInstance()
